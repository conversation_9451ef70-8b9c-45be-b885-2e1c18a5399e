import { areasAtom } from "@/atoms/areas/areas-atom";
import { openConfirmDeleteAreaDialogAtom } from "@/atoms/areas/open-confirm-delete-area-dialog-atom";
import { selectedAreaToDeleteAtom } from "@/atoms/areas/selected-area-to-delete-atom";
import i18n from "@/localization/i18n";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "../ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";

export default function ConfirmDeleteAreaDialog() {
  const { t } = useTranslation();
  const isOpened = openConfirmDeleteAreaDialogAtom.useOpened();

  const { area } = areasAtom.useValue();
  const { id } = selectedAreaToDeleteAtom.useValue();

  const [isLoading, setIsLoading] = useState(false);

  const closeDialog = () => {
    openConfirmDeleteAreaDialogAtom.close();
    selectedAreaToDeleteAtom.change("id", null);
    areasAtom.change("area", null);
  };

  const deleteArea = async () => {
    if (id) {
      setIsLoading(true);
      await areasAtom.deleteArea(id, closeDialog);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      areasAtom.getOneArea(id);
    }
  }, [id]);

  return (
    <Dialog open={isOpened} onOpenChange={closeDialog}>
      <DialogContent dir={i18n.language === "ar" ? "rtl" : "ltr"}>
        <DialogHeader>
          <DialogTitle>
            {t("areas.confirm_delete.title")} ( {area?.name} )
          </DialogTitle>
          <DialogDescription>
            {t("areas.confirm_delete.description")}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" onClick={closeDialog}>
              {t("areas.confirm_delete.cancel")}
            </Button>
          </DialogClose>
          <Button
            variant="destructive"
            onClick={deleteArea}
            disabled={isLoading}
          >
            {isLoading
              ? t("areas.confirm_delete.loading")
              : t("areas.confirm_delete.delete")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
