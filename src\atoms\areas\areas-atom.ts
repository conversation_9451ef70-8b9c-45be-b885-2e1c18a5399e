import type {
  Area,
  CreateAreaFormData,
  CreateAreaResponse,
  DeleteAreaResponse,
  EditAreaFormData,
  EditAreaResponse,
  GetAreasResponse,
  GetOneAreaResponse,
} from "@/types/areas";
import { endpoint } from "@/utils/endpoints";
import { atom } from "@mongez/react-atom";
import { AxiosError } from "axios";
import toast from "react-hot-toast";

interface AreasAtom {
  areas: Area[];
  area: Area | null;
}

interface AreasAtomAction {
  getAreas: () => void;
  createArea: (formData: CreateAreaFormData, onSuccess?: () => void) => void;
  getOneArea: (id: number) => void;
  editArea: (
    id: number,
    formData: EditAreaFormData,
    onSuccess?: () => void,
  ) => void;
  deleteArea: (id: number, onSuccess?: () => void) => void;
}

export const areasAtom = atom<AreasAtom, AreasAtomAction>({
  key: "areas-atom",
  default: {
    areas: [],
    area: null,
  },

  actions: {
    async getAreas() {
      try {
        const { data } = await endpoint.get<GetAreasResponse>("areas");
        areasAtom.change("areas", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async createArea(formData: CreateAreaFormData, onSuccess?: () => void) {
      try {
        const { data } = await endpoint.post<CreateAreaResponse>(
          "areas",
          formData,
        );

        toast.success(data.message);

        onSuccess?.();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async getOneArea(id: number) {
      try {
        const { data } = await endpoint.get<GetOneAreaResponse>(`areas/${id}`);
        areasAtom.change("area", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async deleteArea(id: number, onSuccess?: () => void) {
      try {
        const { data } = await endpoint.delete<DeleteAreaResponse>(
          `areas/${id}`,
        );
        toast.success(data.data);
        onSuccess?.();
        areasAtom.getAreas();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async editArea(
      id: number,
      formData: EditAreaFormData,
      onSuccess?: () => void,
    ) {
      try {
        const { data } = await endpoint.put<EditAreaResponse>(
          `areas/${id}`,
          formData,
        );

        toast.success(data.message);

        onSuccess?.();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },
  },
});
