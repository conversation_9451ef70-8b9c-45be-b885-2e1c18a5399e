// 🔹 Generic API response wrapper
export interface ApiResponse<T> {
  status_code: number;
  status: string | number; // sometimes number, sometimes string
  message: string | null;
  data: T;
}

// 🔹 Core models
export interface Area {
  id: number;
  area_group_id: string;
  area_group: string;
  name: string;
  code: string;
  type: string;
  radius: string;
  color: string;
  is_personal: string;
}

export type Points =
  | {
      lat: number;
      lng: number;
    }[]
  | {
      center: {
        lat: number;
        lng: number;
      };
      radius: number;
    };

// 🔹 Form data types
export interface BaseAreaFormData {
  name: string;
  code: string;
  type: number;
  color: string;
  is_personal: boolean;
  area_group_id: number;
}

export interface CreateAreaFormData extends BaseAreaFormData {
  points: Points;
}

export interface EditAreaFormData extends BaseAreaFormData {
  points?: Points;
}

// 🔹 API responses
export type GetAreasResponse = ApiResponse<Area[]>;
export type GetOneAreaResponse = ApiResponse<Area>;
export type EditAreaResponse = ApiResponse<Area>;
export type DeleteAreaResponse = ApiResponse<string>;

export interface CreateAreaPayload {
  name: string;
  area_group_id: number;
  code: string;
  type: number;
  color: string;
  is_personal: boolean;
  user_id: number;
  updated_at: Date;
  created_at: Date;
  id: number;
}
export type CreateAreaResponse = ApiResponse<CreateAreaPayload>;
