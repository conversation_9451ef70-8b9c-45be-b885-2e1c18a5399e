import { Toaster } from "react-hot-toast";
import { Navigate, Route, Routes } from "react-router";
import { localeAtom } from "./atoms/locale-atom";
import AuthLayout from "./components/layouts/auth-layout";
import BaseLayout from "./components/layouts/base-layout";
import { ThemeProvider } from "./components/theme-provider";
import GuardedRoutes from "./guards/guarded-routes";
import ReverseGuardedRoutes from "./guards/reverse-guarded-routes";
import AddAreaPage from "./pages/areas/add-area-page";
import AreasPage from "./pages/areas/areas-page";
import EditAreaPage from "./pages/areas/edit-area-page";
import LoginPage from "./pages/auth/login-page";
import RegisterPage from "./pages/auth/register-page";
import NotFoundPage from "./pages/not-found-page";
import { URLS } from "./utils/urls";

export default function App() {
  const { locale_code } = localeAtom.useValue();

  return (
    <div dir={locale_code === "ar" ? "rtl" : "ltr"}>
      <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
        <Routes>
          <Route
            path={URLS.auth.auth}
            element={
              <ReverseGuardedRoutes>
                <AuthLayout />
              </ReverseGuardedRoutes>
            }
          >
            <Route
              path={URLS.auth.auth}
              element={<Navigate to={URLS.notFound} replace />}
            />
            <Route path={URLS.auth.login} element={<LoginPage />} />
            <Route path={URLS.auth.register} element={<RegisterPage />} />
          </Route>

          <Route
            path={URLS.home}
            element={
              <GuardedRoutes>
                <BaseLayout />
              </GuardedRoutes>
            }
          >
            {/* <Route path={URLS.units} element={<UnitsPage />} />
            <Route path={URLS.reports} element={<ReportsPage />} /> */}
            <Route path={URLS.areas} element={<AreasPage />} />
            <Route path={URLS.addArea} element={<AddAreaPage />} />
            <Route path={`${URLS.editArea}/:id`} element={<EditAreaPage />} />
          </Route>

          <Route path={URLS.notFound} element={<NotFoundPage />} />
          <Route path="*" element={<Navigate to={URLS.notFound} replace />} />
        </Routes>

        <Toaster position="top-center" reverseOrder={false} />
      </ThemeProvider>
    </div>
  );
}
