import { areaGroups<PERSON><PERSON> } from "@/atoms/area-groups/area-groups-atom";
import { areaShapePointsAtom } from "@/atoms/areas/area-shape-points-atom";
import { areasAtom } from "@/atoms/areas/areas-atom";
import { selectedDrawerType<PERSON>tom } from "@/atoms/areas/selected-drawer-type-atom";
import { Button } from "@/components/ui/button";
import { ColorInput } from "@/components/ui/color-input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { URLS } from "@/utils/urls";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { <PERSON>, useNavigate, useParams } from "react-router";
import { z } from "zod";

const FormSchema = z.object({
  name: z.string().nonempty("Name is required"),
  code: z.string().nonempty("Code is required"),
  type: z.number().min(1, "Type is required"),
  color: z.string().nonempty("Color is required"),
  is_personal: z.boolean(),
  area_group_id: z.number().min(1, "Area Group is required"),
});

export default function EditAreaForm() {
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: "",
      code: "",
      type: 0,
      color: "#000000",
      is_personal: false,
      area_group_id: 0,
    },
  });

  const { areaGroups } = areaGroupsAtom.useValue();

  const { area } = areasAtom.useValue();

  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();

  const { id } = useParams();

  useEffect(() => {
    if (id) {
      areasAtom.getOneArea(+id);
    }
  }, [id]);

  useEffect(() => {
    async function setFormValues() {
      await areaGroupsAtom.getAreaGroups();

      if (area) {
        form.reset({
          name: area.name,
          code: area.code,
          type: +area.type,
          color: area.color,
          is_personal: !!+area.is_personal,
          area_group_id: +area.area_group_id,
        });
      }
    }

    setFormValues();
  }, [area, form]);

  useEffect(() => {
    if (area) {
      selectedDrawerTypeAtom.change("selectedType", +area.type || 0);
    }
  }, [area]);

  const { value: points } = areaShapePointsAtom.useValue();

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    setIsLoading(true);

    if (id) {
      await areasAtom.editArea(
        +id,
        points ? { ...data, points } : { ...data },
        () => {
          navigate(URLS.areas);
          selectedDrawerTypeAtom.change("selectedType", 0);
          areaShapePointsAtom.change("value", null);
        },
      );
    }

    setIsLoading(false);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter area name" {...field} />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Code</FormLabel>
                <FormControl>
                  <Input placeholder="Enter area code" {...field} />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Type</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(Number(value));
                    selectedDrawerTypeAtom.change(
                      "selectedType",
                      Number(value),
                    );
                  }}
                  value={field.value > 0 ? String(field.value) : ""}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="1">circle</SelectItem>
                    <SelectItem value="2">polygon</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="area_group_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Area Group</FormLabel>
                <Select
                  onValueChange={(value) => field.onChange(Number(value))}
                  value={field.value > 0 ? String(field.value) : ""}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Area Group" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {areaGroups?.map((group) => (
                      <SelectItem key={group.id} value={String(group.id)}>
                        {group.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="color"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Color</FormLabel>
                <FormControl>
                  <ColorInput
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select color"
                  />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="is_personal"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-2xl border p-3 shadow">
              <div className="space-y-0.5">
                <FormLabel>Is Personal</FormLabel>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={isLoading} variant={"outline"}>
            {isLoading ? "Editing..." : "Edit"}
          </Button>
          <Link to={URLS.areas}>
            <Button type="button" variant={"outline"}>
              Back
            </Button>
          </Link>
        </div>
      </form>
    </Form>
  );
}
